<template>
  <div class="app-container">
    <!-- 背景粒子效果 -->
    <div class="particles">
      <div v-for="i in 50" :key="i" class="particle" :style="getParticleStyle(i)"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 标题区域 -->
      <div class="hero-section">
        <h1 class="main-title animate__animated animate__bounceInDown animate__delay-1s">
          🚀 欢迎来到未来世界
        </h1>
        <p class="subtitle animate__animated animate__fadeInUp animate__delay-2s">
          体验最炫酷的动画效果
        </p>
      </div>

      <!-- 功能卡片区域 -->
      <div class="cards-container animate__animated animate__fadeInUp animate__delay-3s">
        <div
          v-for="(card, index) in cards"
          :key="index"
          class="feature-card animate__animated"
          :class="card.animation"
          :style="{ animationDelay: (3.5 + index * 0.3) + 's' }"
          @mouseenter="handleCardHover(index)"
          @mouseleave="handleCardLeave(index)"
        >
          <div class="card-icon">{{ card.icon }}</div>
          <h3>{{ card.title }}</h3>
          <p>{{ card.description }}</p>
        </div>
      </div>

      <!-- 交互按钮区域 -->
      <div class="action-section animate__animated animate__fadeInUp animate__delay-5s">
        <el-button
          type="primary"
          size="large"
          class="action-btn"
          @click="triggerExplosion"
          :class="{ 'animate__animated animate__pulse animate__infinite': isExploding }"
        >
          <el-icon><Star /></el-icon>
          点击爆炸效果
        </el-button>

        <el-button
          type="success"
          size="large"
          class="action-btn"
          @click="triggerRainbow"
          :class="{ 'animate__animated animate__heartBeat animate__infinite': isRainbow }"
        >
          <el-icon><MagicStick /></el-icon>
          彩虹动画
        </el-button>
      </div>

      <!-- 爆炸效果元素 -->
      <div v-if="showExplosion" class="explosion-container">
        <div
          v-for="i in 20"
          :key="i"
          class="explosion-particle animate__animated animate__bounceOutUp"
          :style="getExplosionStyle(i)"
        ></div>
      </div>

      <!-- 彩虹效果 -->
      <div v-if="showRainbow" class="rainbow-container">
        <div
          v-for="(color, index) in rainbowColors"
          :key="index"
          class="rainbow-stripe animate__animated animate__slideInLeft"
          :style="{
            backgroundColor: color,
            animationDelay: index * 0.1 + 's',
            top: index * 60 + 'px'
          }"
        ></div>
      </div>
    </div>

    <!-- 浮动元素 -->
    <div class="floating-elements">
      <div
        v-for="i in 8"
        :key="i"
        class="floating-element animate__animated animate__bounce animate__infinite"
        :style="getFloatingStyle(i)"
      >
        {{ getRandomEmoji() }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Star, MagicStick } from '@element-plus/icons-vue'

// 响应式数据
const isExploding = ref(false)
const isRainbow = ref(false)
const showExplosion = ref(false)
const showRainbow = ref(false)

// 卡片数据
const cards = ref([
  {
    icon: '⚡',
    title: '闪电速度',
    description: '极速响应，瞬间加载',
    animation: 'animate__zoomIn'
  },
  {
    icon: '🎨',
    title: '炫酷设计',
    description: '视觉盛宴，美轮美奂',
    animation: 'animate__rotateIn'
  },
  {
    icon: '🚀',
    title: '性能卓越',
    description: '强劲动力，无限可能',
    animation: 'animate__slideInUp'
  },
  {
    icon: '💎',
    title: '品质保证',
    description: '精工细作，完美体验',
    animation: 'animate__flipInX'
  }
])

// 彩虹颜色
const rainbowColors = ['#ff0000', '#ff8000', '#ffff00', '#80ff00', '#00ff00', '#00ff80', '#00ffff', '#0080ff', '#0000ff', '#8000ff', '#ff00ff', '#ff0080']

// 随机表情符号
const emojis = ['⭐', '✨', '🌟', '💫', '🎉', '🎊', '🔥', '💥', '🌈', '🦄']

// 方法
const getParticleStyle = (index) => {
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    animationDelay: Math.random() * 5 + 's',
    animationDuration: (Math.random() * 3 + 2) + 's'
  }
}

const getFloatingStyle = (index) => {
  return {
    left: Math.random() * 90 + '%',
    top: Math.random() * 80 + '%',
    animationDelay: Math.random() * 2 + 's',
    animationDuration: (Math.random() * 2 + 3) + 's',
    fontSize: (Math.random() * 20 + 20) + 'px'
  }
}

const getExplosionStyle = (index) => {
  const angle = (index / 20) * 360
  const distance = Math.random() * 200 + 100
  return {
    left: '50%',
    top: '50%',
    transform: `translate(-50%, -50%) rotate(${angle}deg) translateY(-${distance}px)`,
    animationDuration: '1s'
  }
}

const getRandomEmoji = () => {
  return emojis[Math.floor(Math.random() * emojis.length)]
}

const handleCardHover = (index) => {
  // 可以添加悬停效果
}

const handleCardLeave = (index) => {
  // 可以添加离开效果
}

const triggerExplosion = () => {
  isExploding.value = true
  showExplosion.value = true

  setTimeout(() => {
    isExploding.value = false
    showExplosion.value = false
  }, 2000)
}

const triggerRainbow = () => {
  isRainbow.value = true
  showRainbow.value = true

  setTimeout(() => {
    isRainbow.value = false
    showRainbow.value = false
  }, 3000)
}

onMounted(() => {
  // 页面加载完成后的初始化
})
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 背景粒子效果 */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: float 5s infinite ease-in-out;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 1200px;
  padding: 2rem;
}

/* 标题区域 */
.hero-section {
  margin-bottom: 4rem;
}

.main-title {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.subtitle {
  font-size: 1.5rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 功能卡片区域 */
.cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-10px) scale(1.05);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #fff;
}

.feature-card p {
  opacity: 0.8;
  line-height: 1.6;
}

/* 交互按钮区域 */
.action-section {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  padding: 1rem 2rem;
  font-size: 1.2rem;
  border-radius: 50px;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.action-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* 爆炸效果 */
.explosion-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.explosion-particle {
  position: absolute;
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, #ff6b6b, #feca57);
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
}

/* 彩虹效果 */
.rainbow-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 999;
}

.rainbow-stripe {
  position: absolute;
  width: 100%;
  height: 50px;
  opacity: 0.8;
}

/* 浮动元素 */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  user-select: none;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .cards-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .action-section {
    flex-direction: column;
    align-items: center;
  }

  .main-content {
    padding: 1rem;
  }
}

/* 额外的动画效果 */
.feature-card {
  animation-fill-mode: both;
}

/* 自定义动画 */
@keyframes sparkle {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

.sparkle {
  animation: sparkle 1s infinite;
}
</style>