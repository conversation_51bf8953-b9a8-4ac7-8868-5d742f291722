<template>
  <div style="padding: 30px">
    <el-button type="primary" @click="openAddDialog">添加</el-button>
    <el-divider></el-divider>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" border stripe>
      <el-table-column prop="id" label="ID" />
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column label="操作" width="140px" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="openEditDialog(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="margin-top: 20px; display: flex; justify-content: center">
      <el-pagination
        background
        v-model:currentPage="pageParams.pageNum"
        v-model:page-size="pageParams.pageSize"
        :total="total"
        :pager-count="7"
        @current-change="currentChange"
        @size-change="sizeChange"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next, jumper"
      />
    </div>
  </div>

  <el-dialog v-model="dialogParam.visible" :title="dialogParam.title" width="650px">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="form.username" placeholder="请输入用户名" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入邮箱" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="submitForm">
          {{ dialogParam.type === "edit" ? "更新" : "添加" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { markRaw, ref, onMounted } from "vue"
import { ElMessage, ElMessageBox } from "element-plus"

const tableData = ref([])
const loading = ref(false)
const dialogParam = ref({
  visible: false,
  type: "add",
  title: "添加用户"
})
const currentData = ref({})
const formRef = ref(null)
const form = ref({
  id: "",
  username: "",
  email: ""
})
const rules = markRaw({
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    { min: 2, max: 20, message: "用户名长度应为2-20个字符", trigger: "blur" }
  ],
  email: [
    { required: true, message: "请输入邮箱地址", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
  ]
})
const total = ref(0)
const pageParams = ref({
  pageNum: 1,
  pageSize: 10
})

onMounted(() => {
  getDataList()
})

const getDataList = async () => {
  loading.value = true
  setTimeout(() => {
    tableData.value = [
      { id: 1, username: "张三", email: "<EMAIL>" },
      { id: 2, username: "李四", email: "<EMAIL>" },
      { id: 3, username: "王五", email: "<EMAIL>" }
    ]
    loading.value = false
  }, 500)
}

const currentChange = current => {
  pageParams.value.pageNum = current
  getDataList()
}
const sizeChange = size => {
  pageParams.value.pageNum = 1
  pageParams.value.pageSize = size
  getDataList()
}

// TODO 重置表单
const resetForm = () => {
  form.value.id = ""
  form.value.username = ""
  form.value.email = ""
  currentData.value = {}
  formRef.value?.clearValidate()
}

// TODO 添加
const openAddDialog = () => {
  resetForm()
  dialogParam.value = {
    visible: true,
    type: "add",
    title: "添加"
  }
}

// 修改
const openEditDialog = row => {
  resetForm()
  currentData.value = { ...row }
  Object.assign(form.value, row)
  dialogParam.value = {
    visible: true,
    type: "edit",
    title: "编辑"
  }
}

// TODO 提交添加和修改
const submitForm = async () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      if (dialogParam.value.type === "edit") {
        console.log("修改", form.value)
      } else {
        console.log("添加", form.value)
      }
      dialogParam.value.visible = false
    }
  })
}

// TODO 确认删除
const handleDelete = row => {
  ElMessageBox.confirm("确定要删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      console.log("删除", row)
    })
    .catch(() => {
      console.log("cancel")
    })
}

const handleCancel = async () => {
  dialogParam.value.visible = false
}
</script>

<style lang="scss" scoped></style>
